"use strict";

module.exports = (sequelize, DataTypes) => {
  const app_log = sequelize.define(
    "app_log",
    {
      app_log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      
      // User identification - using same foreign key names as existing tables
      admin_id: DataTypes.INTEGER,
      company_id: DataTypes.INTEGER,
      staff_id: DataTypes.INTEGER,
      customer_id: DataTypes.INTEGER,
      
      // User type to identify which user performed the action
      user_type: {
        type: DataTypes.ENUM("admin", "company", "staff", "customer"),
        allowNull: false,
      },
      
      // Session and device information
      access_token: DataTypes.STRING,
      device_type: {
        type: DataTypes.ENUM("ios", "android", "web"),
        allowNull: true,
      },
      device_token: DataTypes.STRING,
      ip_address: {
        type: DataTypes.STRING(45), // IPv6 support
        allowNull: true,
      },
      user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      
      // Action details
      action_type: {
        type: DataTypes.ENUM(
          "login",
          "logout", 
          "stage_access",
          "item_add",
          "item_edit",
          "item_duplicate",
          "item_delete",
          "item_assign_to_storage",
          "item_remove_from_storage",
          "item_remove_from_inventory",
          "unit_mapping",
          "unit_move_to_rack",
          "unit_scan",
          "item_scan",
          "shipment_create",
          "shipment_edit",
          "stage_add",
          "stage_edit",
          "stage_delete",
          "stage_deactivate",
          "shipment_type_add",
          "shipment_type_edit",
          "room_add",
          "room_edit",
          "room_deactivate",
          "room_delete",
          "item_list_add",
          "item_list_edit",
          "item_list_deactivate",
          "item_list_delete",
          "tag_add",
          "tag_edit",
          "tag_deactivate",
          "tag_delete",
          "user_add",
          "user_edit",
          "user_deactivate",
          "user_delete",
          "customer_add",
          "customer_edit",
          "customer_deactivate",
          "customer_delete",
          "view_items",
          "signature_add"
        ),
        allowNull: false,
      },
      
      // Context information - using same foreign key names as existing tables
      shipment_job_id: DataTypes.INTEGER,
      shipment_inventory_id: DataTypes.INTEGER,
      unit_id: DataTypes.INTEGER,
      stage_id: DataTypes.INTEGER,
      local_stage_id: DataTypes.INTEGER,
      room_id: DataTypes.INTEGER,
      qr_id: DataTypes.INTEGER,
      tag_id: DataTypes.INTEGER,
      
      // Action details and metadata
      action_description: DataTypes.TEXT,
      
      // Specific tracking fields
      items_count: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Number of items affected in the action",
      },
      
      // Scan method tracking
      scan_method: {
        type: DataTypes.ENUM("manual", "qr_scan"),
        allowNull: true,
      },
      
      // Stage specific tracking
      stage_name: DataTypes.STRING,
      
      // Item modification tracking
      modified_fields: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "JSON object containing the fields that were modified",
      },
      
      // Previous and new values for audit trail
      old_values: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "JSON object containing previous values",
      },
      new_values: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "JSON object containing new values",
      },
      
      // Additional metadata
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "Additional context-specific data",
      },
      
      // Status and result
      action_status: {
        type: DataTypes.ENUM("success", "failed", "partial"),
        allowNull: false,
        defaultValue: "success",
      },
      
      error_message: DataTypes.TEXT,
      
      // Timestamps
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    },
    {
      createdAt: false,
      updatedAt: false,
      indexes: [
        {
          fields: ["user_type", "created_at"],
        },
        {
          fields: ["action_type", "created_at"],
        },
        {
          fields: ["shipment_job_id", "created_at"],
        },
        {
          fields: ["admin_id", "created_at"],
        },
        {
          fields: ["company_id", "created_at"],
        },
        {
          fields: ["staff_id", "created_at"],
        },
        {
          fields: ["customer_id", "created_at"],
        },
        {
          fields: ["created_at"],
        },
      ],
    }
  );

  app_log.associate = function (models) {
    // User associations - using same foreign key names as existing tables
    app_log.belongsTo(models.admin, {
      as: "log_admin",
      foreignKey: "admin_id",
    });

    app_log.belongsTo(models.company, {
      as: "log_company",
      foreignKey: "company_id",
    });

    app_log.belongsTo(models.staff, {
      as: "log_staff",
      foreignKey: "staff_id",
    });

    app_log.belongsTo(models.customer, {
      as: "log_customer",
      foreignKey: "customer_id",
    });

    // Context associations - using same foreign key names as existing tables
    app_log.belongsTo(models.shipment_job, {
      as: "log_shipment_job",
      foreignKey: "shipment_job_id",
    });

    app_log.belongsTo(models.shipment_inventory, {
      as: "log_shipment_inventory",
      foreignKey: "shipment_inventory_id",
    });

    app_log.belongsTo(models.unit_list, {
      as: "log_unit",
      foreignKey: "unit_id",
    });

    app_log.belongsTo(models.shipment_type_stage, {
      as: "log_stage",
      foreignKey: "stage_id",
    });

    app_log.belongsTo(models.shipment_type_stage_for_shipment, {
      as: "log_local_stage",
      foreignKey: "local_stage_id",
    });

    app_log.belongsTo(models.shipment_room, {
      as: "log_room",
      foreignKey: "room_id",
    });

    app_log.belongsTo(models.qr_code, {
      as: "log_qr",
      foreignKey: "qr_id",
    });

    app_log.belongsTo(models.tag, {
      as: "log_tag",
      foreignKey: "tag_id",
    });
  };

  return app_log;
};
