"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable("app_logs", {
        app_log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false,
        },
        
        // User identification - using same foreign key names as existing tables
        admin_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "admins",
            key: "admin_id",
          },
        },
        company_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "companies",
            key: "company_id",
          },
        },
        staff_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "staff",
            key: "staff_id",
          },
        },
        customer_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "customers",
            key: "customer_id",
          },
        },
        
        // User type to identify which user performed the action
        user_type: {
          type: Sequelize.ENUM("admin", "company", "staff", "customer"),
          allowNull: false,
        },
        
        // Session and device information
        access_token: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        device_type: {
          type: Sequelize.ENUM("ios", "android", "web"),
          allowNull: true,
        },
        device_token: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        ip_address: {
          type: Sequelize.STRING(45), // IPv6 support
          allowNull: true,
        },
        user_agent: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        
        // Action details
        action_type: {
          type: Sequelize.ENUM(
            "login",
            "logout", 
            "stage_access",
            "item_add",
            "item_edit",
            "item_duplicate",
            "item_delete",
            "item_assign_to_storage",
            "item_remove_from_storage",
            "item_remove_from_inventory",
            "unit_mapping",
            "unit_move_to_rack",
            "unit_scan",
            "item_scan",
            "shipment_create",
            "shipment_edit",
            "stage_add",
            "stage_edit",
            "stage_delete",
            "stage_deactivate",
            "shipment_type_add",
            "shipment_type_edit",
            "room_add",
            "room_edit",
            "room_deactivate",
            "room_delete",
            "item_list_add",
            "item_list_edit",
            "item_list_deactivate",
            "item_list_delete",
            "tag_add",
            "tag_edit",
            "tag_deactivate",
            "tag_delete",
            "user_add",
            "user_edit",
            "user_deactivate",
            "user_delete",
            "customer_add",
            "customer_edit",
            "customer_deactivate",
            "customer_delete",
            "view_items",
            "signature_add"
          ),
          allowNull: false,
        },
        
        // Context information - using same foreign key names as existing tables
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "shipment_jobs",
            key: "shipment_job_id",
          },
        },
        shipment_inventory_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "shipment_inventories",
            key: "shipment_inventory_id",
          },
        },
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "unit_lists",
            key: "unit_id",
          },
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "shipment_type_stages",
            key: "shipment_stage_id",
          },
        },
        local_stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "shipment_type_stage_for_shipments",
            key: "local_shipment_stage_id",
          },
        },
        room_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "shipment_rooms",
            key: "room_id",
          },
        },
        qr_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "qr_codes",
            key: "qr_id",
          },
        },
        tag_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "tags",
            key: "tag_id",
          },
        },
        
        // Action details and metadata
        action_description: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        
        // Specific tracking fields
        items_count: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Number of items affected in the action",
        },
        
        // Scan method tracking
        scan_method: {
          type: Sequelize.ENUM("manual", "qr_scan"),
          allowNull: true,
        },
        
        // Stage specific tracking
        stage_name: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        
        // Item modification tracking
        modified_fields: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "JSON object containing the fields that were modified",
        },
        
        // Previous and new values for audit trail
        old_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "JSON object containing previous values",
        },
        new_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "JSON object containing new values",
        },
        
        // Additional metadata
        metadata: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Additional context-specific data",
        },
        
        // Status and result
        action_status: {
          type: Sequelize.ENUM("success", "failed", "partial"),
          allowNull: false,
          defaultValue: "success",
        },
        
        error_message: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        
        // Timestamps
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
      });
      
      // Add indexes for better query performance
      await queryInterface.addIndex("app_logs", ["user_type", "created_at"]);
      await queryInterface.addIndex("app_logs", ["action_type", "created_at"]);
      await queryInterface.addIndex("app_logs", ["shipment_job_id", "created_at"]);
      await queryInterface.addIndex("app_logs", ["admin_id", "created_at"]);
      await queryInterface.addIndex("app_logs", ["company_id", "created_at"]);
      await queryInterface.addIndex("app_logs", ["staff_id", "created_at"]);
      await queryInterface.addIndex("app_logs", ["customer_id", "created_at"]);
      await queryInterface.addIndex("app_logs", ["created_at"]);
      
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("app_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
