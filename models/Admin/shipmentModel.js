const {
	shipment_job,
	company,
	shipment_type_stage,
	shipment_type,
	shipment_job_assign_worker,
	shipment_job_assign_worker_list,
	customer,
	shipment_job_forced,
	shipment_job_signature,
	shipment_type_for_shipment,
	shipment_type_stage_for_shipment,
	qr_code,
	staff,
	unit_list,
	shipment_inventory,
	shipment_inventory_photo,
	shipment_room,
	shipment_inventory_exception,
	shipment_inventory_comments,
	shipment_inventory_notes,
	shipment_exception,
	shipment_inventory_exception_note,
	shipment_inventory_location,
	shipment_location,
	tag_shipment,
	tag,
	tag_item,
	sequelize,
	shipment_job_document
} = require("../../database/schemas");
const {
	generateResponse,
} = require("../../assets/common");
const { where, fn, col, Op } = require("sequelize");
const staffModel = require("../Admin/staffModel");
const homeModel = require("../../models/APP/homeModel");
const moment = require('moment');
exports.getAssignShipmentTypeStages = async (shipmentId) => {
	return await shipment_type_stage_for_shipment.findAll({
		where: {
			shipment_job_id: shipmentId
		},
		order: [["order_of_stages", "ASC"]],
	});
}

exports.deleteExitingWorkerFromStage = async (userDetails) => {
	return await shipment_job_assign_worker_list
		.destroy({
			where: {
				assign_job_worker_id: userDetails.assign_job_worker_id,
				shipment_job_id: userDetails.shipment_job_id,
				role: userDetails.role,
			},
		});
}

exports.isSupervisorAssignToShipmentStage = async (shipment_job_id, stageId, role) => {
	const data = await shipment_job_assign_worker_list
		.findOne({
			where: {
				shipment_job_id: shipment_job_id,
				local_shipment_stage_id: stageId,
				role: role,
			},
		});
	return data
}

exports.assignNewStaffToShipment = async (shipment_job_id, role, staff_worker_id, stageId) => {
	return await shipment_job_assign_worker_list.create({
		shipment_job_id: shipment_job_id,
		staff_id: staff_worker_id,
		local_shipment_stage_id: stageId,
		role: role,
	});
}


exports.isStaffAlreadyAssignToStage = async (shipment_job_id, staff_worker_id, stageId) => {
	const isValidStaff = await shipment_job_assign_worker_list
		.findOne({
			where: {
				shipment_job_id: shipment_job_id,
				staff_id: staff_worker_id,
				local_shipment_stage_id: stageId,
			},
		});
	return isValidStaff
}


exports.addDocument = async (shipmentId, mediaFileList) => {
	// await shipment_job_document.create(
	// 	{
	// 		shipment_job_id: shipmentId,
	// 		pdf: newFileName,
	// 		name: file.originalname
	// 	}
	// )

	const multiData = [];

	mediaFileList.forEach((element) => {
		let Options = {};
		Options["shipment_job_id"] = shipmentId;
		Options["pdf"] = element.media;
		Options["name"] = element.name;
		multiData.push(Options);
	});
	const multiDataCreate = await shipment_job_document.bulkCreate(
		multiData,
		{
			returning: true,
		}
	);
}

exports.updateShipmentStorageId = async (shipmentId, shipmentIdStorage) => {
	const data = await shipment_job.update(
		{ storage_shipment_job_id: shipmentIdStorage },
		{ where: { shipment_job_id: shipmentId } }
	);
	return data
}

exports.checkShipmentAssignToStorage = async (body) =>
	await shipment_job.findOne({ where: { shipment_job_id: body.shipment_job_id } });

exports.deleteDocument = async (pdfId) =>
	await shipment_job_document.destroy({ where: { shipment_job_document_id: pdfId } });

exports.fetchShipmentForStorageDelete = async (shipmentId) => {
	return shipment_job.findOne({
		where: {
			shipment_job_id: shipmentId
		},
		attributes: [
			"shipment_job_id",
			"storage_shipment_job_id",
			"status",
			"email",
			[
				sequelize.literal(
					"(select integration_key FROM `company_integration_keys` where company_id = shipment_job.company_id) "
				),
				"integration_key",
			],
		]
	})
}

exports.fetchShipmentWarehouse = async (shipmentId) => {
	return await shipment_job.findOne({
		where: {
			shipment_job_id: shipmentId
		},
		attributes: ["shipment_job_id", "warehouseId"]
	})
}

exports.fetchShipmentItemCount = async (shipmentId, warehouseId) => {
	return await unit_list.findAndCountAll({
		where: {
			shipment_job_id: shipmentId,
			warehouseId: warehouseId
		},
	})
}

exports.fetchShipmentTypeForShipment = async (body) => {
	return await shipment_type.findOne({
		where: {
			shipment_type_id: body.shipment_type_id
		},
	});
}

exports.shipmentStorageCheck = async (body) => {
	return shipment_job.findOne({
		where: {
			shipment_job_id: body.job_id
		}
	})
}

exports.createShipmentTypeForShipment = async (body, shipmentData) => {
	const shipmentObj = {
		name: body.name,
		admin_id: body.admin_id,
		company_id: body.company_id,
		staff_id: body.staff_id,
		number_of_stages: body.number_of_stages,
		is_pickup_date_mandatory: body.is_pickup_date_mandatory,
		is_make_user_mandatory: body.is_make_user_mandatory,
		signature_require: body.signature_require,
		status: body.status,
		ref_shipment_type_id: body.shipment_type_id,
		shipment_job_id: shipmentData.shipment_job_id,
		is_deleted: body.is_deleted,
	}
	return await shipment_type_for_shipment.create(shipmentObj);
}

exports.upShipment = async (createShipmentTypeForShipment, shipmentId) => {
	const data = await shipment_job.update(
		{
			local_shipment_type_id: createShipmentTypeForShipment.local_shipment_type_id
		},
		{
			where: { shipment_job_id: shipmentId },
		}
	);
	return data
}


exports.upShipmentStageUpdate = async (createShipmentTypeStagesForShipment, shipmentId) => {
	const data = await shipment_job.update(
		{
			local_job_status: createShipmentTypeStagesForShipment[0].local_shipment_stage_id
		},
		{
			where: { shipment_job_id: shipmentId },
		}
	);

	return data
}



exports.createShipmentScript = async (body, job_number, i) => {
	return shipment_job.create({
		shipment_name: body.shipment_name + i,
		job_number: job_number,
		shipment_type_id: body.shipment_type_id,
		email: body.email,
		customer_id: body.customer_id,
		company_id: body.company_id,
		job_status: await getShipmentInitialStageModel(body.shipment_type_id),
	})
}

exports.modelResponseShipmentComplete = async (shipmentId) => {
	const data = await shipment_job.update(
		{
			is_job_complete_flag: 1
		},
		{
			where: { shipment_job_id: shipmentId },
		}
	);
	return data
}

exports.isStorageShipmentExitsModel = async (body) => {
	console.log(body.storage_shipment_job_id)
	const shipmentData = await shipment_job.findOne({
		where: {
			storage_shipment_job_id: body.storage_shipment_job_id
		},
		attributes: ["shipment_job_id", "storage_shipment_job_id", "warehouseId"]
	})
	return shipmentData;
}

exports.addShipmentModel = async (bodyFunction) => {
	let shipmentJob = await shipment_job.create({
		...bodyFunction,
		// job_status: await getShipmentInitialStageModel(bodyFunction.shipment_type_id),
	});
	if (shipmentJob) {
		let geStaffListByCompanyId = await staffModel.getStaffListByCompanyId(shipmentJob.company_id);
		for (let i = 0; i < geStaffListByCompanyId.length; i++) {
			let shipmentJobAssignToOldAdminStaff = await staffModel.shipmentJobAssignToOldAdminStaff(geStaffListByCompanyId[i].staff_id, shipmentJob.shipment_job_id);
		}
	}
	return shipmentJob;
}

const getShipmentDetailByCustomerId = async (customerId) =>
	await shipment_job.findAndCountAll({
		where: {
			customer_id: customerId,
		},
	});


exports.shipmentJobAssignToNewAdminStaff = async (jobId, staffId) => {
	const staffDetails = {
		shipment_job_id: jobId,
		staff_id: staffId,
		role: "admin",
	}
	return await shipment_job_assign_worker.create(staffDetails);
}


exports.getShipmentJobListByCompanyId = async (CompanyId) => {
	let shipmentLists = await shipment_job.findAll({
		where: {
			company_id: CompanyId,
		},
	});
	return shipmentLists;

}


exports.updateShipmentModel = async (shipmentId, defaults) => {
	const data = await shipment_job.update(defaults, {
		where: { shipment_job_id: shipmentId },
	});
	return data
}

exports.updateShipmentWebhook = async (shipmentUpdateData, shipmentId) => {
	const data = await shipment_job.update(
		shipmentUpdateData,
		{
			where: { storage_shipment_job_id: shipmentId },
		}
	);
	return data
}

exports.updateUnitWebhook = async (unitUpdateData, unitId) => {
	const data = await unit_list.update(
		unitUpdateData,
		{
			where: { storage_unit_id: unitId },
		}
	);
	return data
}

exports.DeleteUnitWebhook = async (unitId) => {
	const data = await unit_list.destroy(
		{
			where: { storage_unit_id: unitId },
		}
	);
	return data
}

exports.shipmentStorageIdUpdate = async (request) => {
	try {
		return shipment_job.update(
			{
				storage_shipment_job_id: request.shipmentIdStorage,
			},
			{ where: { shipment_job_id: request.shipment_job_id } }
		);
	}
	catch (err) {
		console.log("err", err);
	}
}

exports.deleteShipmentItems = async (shipmentId) => {
	await shipment_inventory.destroy({ where: { shipment_job_id: shipmentId } })
}

exports.isItemsAssignToShipmentTypeStageModel = async (stageId) => {
	return await shipment_inventory.findAndCountAll({
		where: {
			inventory_stage_id: stageId,
		},
	})
}

exports.deleteShipmentTypeStageModel = async (stageId) =>
	await shipment_type_stage_for_shipment.destroy({ where: { local_shipment_stage_id: stageId } })

exports.deleteShipmentModel = async (shipmentId) =>
	await shipment_job.destroy({ where: { shipment_job_id: shipmentId } });

exports.fetchShipmentForStorage = async (request) => {
	const shipmentId = request.shipmentId
	return shipment_job.findByPk(shipmentId, {
		include: [
			{
				model: shipment_type_for_shipment,
				as: "shipment_type_for_shipment",
				attributes: [
					"local_shipment_type_id",
					[
						sequelize.literal(
							"(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
						),
						"current_job_stage",
					],
					[
						sequelize.literal(
							"(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
						),
						"total_stages",
					],
				],
				include: [
					{
						model: shipment_type_stage_for_shipment,
						as: "local_shipment_stage",
						attributes: [
							...BASIC_JOB_STAGES_ATTRIBUTES2,
							[
								sequelize.literal(
									`(select shipment_job_signatures.created_at from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
								),
								"created_at",
							],
							[
								sequelize.literal(
									`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
									}',customer_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
								),
								"customer_signature",
							],
							[
								sequelize.literal(
									`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
									}',supervisor_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id order by shipment_job_signature_id desc limit 1)`
								),
								"supervisor_signature",
							],
						],
						order: "order_of_stages",
						where: { status: "active" },
						include: [
							{
								model: shipment_job_forced,
								as: "shipment_job_forced",
								required: false,
								attributes: COMMON_JOB_FORCED_SHIPMENT_ATTRIBUTES2,
								where: { shipment_job_id: shipmentId },
							},
						],
					},
				],
			},
			{
				model: customer,
				as: "customer_job",
				attributes: COMMON_CUSTOMER_ATTRIBUTES,
			},
			{
				model: company,
				as: "job_company",
				attributes: [
					...COMMON_COMPANY_ATTRIBUTES,
					[
						sequelize.literal(
							`(CASE WHEN job_company.photo IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE_Company_Profile}', 'original/', job_company.photo) END)`
						),
						"company_logo",
					],
				],
			},
		],
		attributes: [...CUSTOMER_JOB_ATTRIBUTES_FOR_STORAGE, "shipment_name"],
	});
}

exports.getShipmentAssignWorkerData = async (shipmentId) => {
	const data = await shipment_job_assign_worker_list.findAll({
		where: {
			shipment_job_id: shipmentId
		},
		attributes: [
			"staff_id",
			"role",
			[
				sequelize.literal("(select first_name FROM `staffs` where staff_id = shipment_job_assign_worker_list.staff_id) "),
				"first_name",
			],
			[
				sequelize.literal("(select last_name FROM `staffs` where staff_id = shipment_job_assign_worker_list.staff_id) "),
				"last_name",
			],
		],
		include: [
			{
				model: staff,
				attributes: [],
				required: true,
				as: "assign_worker_detail",
			},
		],
	})
	return data
}

exports.getShipmentDetailModel = async (company_id, shipmentId, orderingMethod = 'created_at', orderingWay = 'DESC') => {
	// Pre-calculate constants to avoid repeated concatenation
	const AWS_SIGNATURE_PATH = `${Const_AWS_BASE_Job_Signature}${shipmentId}/original/`;
	const AWS_ITEM_PATH = `${Const_AWS_BASE_Job_Item}${shipmentId}/original/`;
	const AWS_INVENTORY_PATH = `${Const_AWS_BASE_Shipment_inventory}`;
	const AWS_COMPANY_PATH = `${Const_AWS_BASE_Company_Profile}original/`;
	const AWS_DOCUMENT_PATH = `${Const_AWS_BASE_shipment_Document}original/`;

	// Build dynamic where clause more efficiently
	const whereClause = company_id
		? { shipment_job_id: shipmentId, company_id }
		: { shipment_job_id: shipmentId };

	// Pre-define reusable subqueries as variables to avoid repetition
	const baseInventoryFilter = `shipment_job_id = shipment_job.shipment_job_id AND deletedAt IS NULL`;
	const activeStageFilter = `local_shipment_type_id = shipment_job.local_shipment_type_id AND status = 'active'`;

	const dbValue = await shipment_job.findOne({
		where: whereClause,
		include: [
			{
				model: shipment_type_for_shipment,
				as: "shipment_type_for_shipment",
				attributes: [
					"local_shipment_type_id",
					"ref_shipment_type_id",
					"name",
					"number_of_stages",
					"signature_require",
					"status",
					// Optimized subqueries with proper indexing hints
					[
						sequelize.literal(
							`(SELECT order_of_stages FROM shipment_type_stage_for_shipments 
							 WHERE local_shipment_stage_id = shipment_job.local_job_status LIMIT 1)`
						),
						"current_job_stage",
					],
					[
						sequelize.literal(
							`(SELECT COUNT(*) FROM shipment_type_stage_for_shipments 
							 WHERE ${activeStageFilter})`
						),
						"total_stages",
					],
				],
				include: [
					{
						model: shipment_type_stage_for_shipment,
						as: "local_shipment_stage",
						attributes: [
							...BASIC_JOB_STAGES_ATTRIBUTES2,
							"order_of_stages",

							// Consolidated inventory counts with single queries where possible
							[
								sequelize.literal(
									`(SELECT COUNT(*) FROM shipment_inventories 
									 WHERE ${baseInventoryFilter})`
								),
								"total_items_inventory",
							],

							// Storage unit queries - optimized with EXISTS for better performance
							[
								sequelize.literal(
									`(SELECT COUNT(*) FROM shipment_inventory_unit_histories siuh
									 WHERE siuh.shipment_job_id = shipment_job.shipment_job_id 
									 AND siuh.deletedAt IS NULL 
									 AND siuh.storage_unit_id IS NOT NULL 
									 AND siuh.add_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id)`
								),
								"total_items_storage",
							],

							[
								sequelize.literal(
									`(SELECT COUNT(*) FROM shipment_inventory_unit_histories siuh
									 WHERE siuh.shipment_job_id = shipment_job.shipment_job_id 
									 AND siuh.deletedAt IS NULL 
									 AND siuh.storage_unit_id IS NOT NULL 
									 AND siuh.remove_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id)`
								),
								"total_remove_items_storage",
							],

							// Weight calculations - optimized
							[
								sequelize.literal(
									`(SELECT COALESCE(SUM(weight), 0) FROM shipment_inventories 
									 WHERE ${baseInventoryFilter} 
									 AND inventory_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id)`
								),
								"total_items_weight_in_shipment",
							],

							[
								sequelize.literal(
									`(SELECT COALESCE(SUM(weight), 0) FROM shipment_inventories 
									 WHERE ${baseInventoryFilter} 
									 AND remove_from_inventory_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id)`
								),
								"total_remove_item_weight_in_shipment",
							],

							[
								sequelize.literal(
									`(SELECT COALESCE(SUM(item_weight), 0) FROM shipment_inventory_unit_histories siuh
									 WHERE siuh.shipment_job_id = shipment_job.shipment_job_id 
									 AND siuh.deletedAt IS NULL 
									 AND siuh.storage_unit_id IS NOT NULL 
									 AND siuh.add_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id)`
								),
								"total_items_weight_in_storage",
							],

							[
								sequelize.literal(
									`(SELECT COALESCE(SUM(item_weight), 0) FROM shipment_inventory_unit_histories siuh
									 WHERE siuh.shipment_job_id = shipment_job.shipment_job_id 
									 AND siuh.deletedAt IS NULL 
									 AND siuh.storage_unit_id IS NOT NULL 
									 AND siuh.remove_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id)`
								),
								"total_remove_items_weight_from_storage",
							],

							// Signature queries - optimized with single query
							[
								sequelize.literal(
									`(SELECT sjs.created_at 
									 FROM shipment_job_signatures sjs 
									 INNER JOIN shipment_type_stage_for_shipments stss ON sjs.local_stage = stss.local_shipment_stage_id 
									 WHERE sjs.shipment_job_id = ${shipmentId} 
									 AND stss.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  
									 ORDER BY sjs.shipment_job_signature_id DESC LIMIT 1)`
								),
								"created_at",
							],

							// Duplicate created_at_date removed (same as created_at)
							[
								sequelize.literal(
									`(SELECT CONCAT('${AWS_SIGNATURE_PATH}', sjs.customer_signature)
									 FROM shipment_job_signatures sjs 
									 INNER JOIN shipment_type_stage_for_shipments stss ON sjs.local_stage = stss.local_shipment_stage_id 
									 WHERE sjs.shipment_job_id = ${shipmentId} 
									 AND stss.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  
									 ORDER BY sjs.shipment_job_signature_id DESC LIMIT 1)`
								),
								"customer_signature",
							],

							[
								sequelize.literal(
									`(SELECT CONCAT('${AWS_SIGNATURE_PATH}', sjs.supervisor_signature)
									 FROM shipment_job_signatures sjs 
									 INNER JOIN shipment_type_stage_for_shipments stss ON sjs.local_stage = stss.local_shipment_stage_id 
									 WHERE sjs.shipment_job_id = ${shipmentId} 
									 AND stss.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id 
									 ORDER BY sjs.shipment_job_signature_id DESC LIMIT 1)`
								),
								"supervisor_signature",
							],

							[
								sequelize.literal(
									`(SELECT sjs.customer_name 
									 FROM shipment_job_signatures sjs 
									 INNER JOIN shipment_type_stage_for_shipments stss ON sjs.local_stage = stss.local_shipment_stage_id 
									 WHERE sjs.shipment_job_id = ${shipmentId} 
									 AND stss.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  
									 ORDER BY sjs.shipment_job_signature_id DESC LIMIT 1)`
								),
								"customer_name",
							],

							[
								sequelize.literal(
									`(SELECT sjs.supervisor_name 
									 FROM shipment_job_signatures sjs 
									 INNER JOIN shipment_type_stage_for_shipments stss ON sjs.local_stage = stss.local_shipment_stage_id 
									 WHERE sjs.shipment_job_id = ${shipmentId} 
									 AND stss.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id 
									 ORDER BY sjs.shipment_job_signature_id DESC LIMIT 1)`
								),
								"supervisor_name",
							],

							// Boolean conversions - simplified
							[sequelize.literal(`supervisor_signature_require_at_origin_to_all_pages = '1'`), "supervisor_signature_require_at_origin_to_all_pages"],
							[sequelize.literal(`supervisor_signature_require_at_destination_to_all_pages = '1'`), "supervisor_signature_require_at_destination_to_all_pages"],
							[sequelize.literal(`customer_signature_require_at_origin_to_all_pages = '1'`), "customer_signature_require_at_origin_to_all_pages"],
							[sequelize.literal(`customer_signature_require_at_destination_to_all_pages = '1'`), "customer_signature_require_at_destination_to_all_pages"],
						],
						order: "order_of_stages",
						where: { status: "active" },
						include: [
							{
								model: shipment_job_forced,
								as: "shipment_job_forced",
								required: false,
								attributes: COMMON_JOB_FORCED_SHIPMENT_ATTRIBUTES2,
								where: { shipment_job_id: shipmentId },
							},
						],
					},
				],
			},

			{
				model: tag_shipment,
				as: "shipment_tag",
				required: false,
				attributes: [COMMON_JOB_TAG_ATTRIBUTES[0]],
				include: {
					model: tag,
					as: "m2m_tag",
					attributes: COMMON_TAG_ATTRIBUTES,
				},
			},

			{
				model: customer,
				as: "customer_job",
				attributes: COMMON_CUSTOMER_ATTRIBUTES,
			},

			{
				model: shipment_inventory,
				where: { shipment_inventory_id: -1 }, // This seems suspicious - might be filtering out all records
				attributes: [
					...COMMON_INVENTORY_ATTRIBUTES,
					// Optimized scanning and override checks
					[
						sequelize.literal(
							`EXISTS(SELECT 1 FROM shipment_inventory_job_scanned sijs 
							 WHERE sijs.local_current_stage_id = shipment_job.local_job_status 
							 AND sijs.shipment_inventory_id = job_items.shipment_inventory_id 
							 AND sijs.shipment_job_id = job_items.shipment_job_id)`
						),
						"isScanned",
					],

					[sequelize.literal(`is_carton = '1'`), "is_carton"],

					[
						sequelize.literal(
							`EXISTS(SELECT 1 FROM shipment_inventory_forced sif 
							 WHERE sif.local_current_stage_id = shipment_job.local_job_status 
							 AND sif.shipment_inventory_id = job_items.shipment_inventory_id)`
						),
						"isOverride",
					],

					// Simplified boolean conversions
					[sequelize.literal(`packed_by_owner = '1'`), "packed_by_owner"],
					[sequelize.literal(`disassembled_by_owner = '1'`), "disassembled_by_owner"],
					[sequelize.literal(`is_disassembled = '1'`), "is_disassembled"],
					[sequelize.literal(`is_electronics = '1'`), "is_electronics"],
					[sequelize.literal(`is_high_value = '1'`), "is_high_value"],
					[sequelize.literal(`is_pro_gear = '1'`), "is_pro_gear"],
					[sequelize.literal(`is_firearm = '1'`), "is_firearm"],
					[sequelize.literal(`isManualLabel = '1'`), "isManualLabel"],
				],
				required: false,
				as: "job_items",
				include: [
					{
						model: shipment_room,
						as: "room",
						attributes: ["name"],
					},
					{
						model: shipment_inventory_comments,
						attributes: ["id", "shipment_inventory_id", "comment"],
						as: "comments",
					},
					{
						model: shipment_inventory_notes,
						attributes: ["id", "shipment_inventory_id", "note"],
						as: "inventory_notes",
					},
					{
						model: shipment_inventory_exception_note,
						attributes: [["shipment_inventory_exception_note_id", "note_id"], "notes"],
						required: false,
						as: "exceptions",
						include: [
							{
								model: shipment_inventory_exception,
								required: false,
								attributes: [
									"shipment_exception_id",
									[
										sequelize.literal(
											"(SELECT name FROM shipment_exceptions se WHERE se.shipment_exception_id = `job_items->exceptions->eid`.shipment_exception_id LIMIT 1)"
										),
										"exception_name",
									],
								],
								as: "eid",
							},
							{
								model: shipment_inventory_location,
								required: false,
								attributes: [
									"shipment_location_id",
									[
										sequelize.literal(
											"(SELECT name FROM shipment_locations sl WHERE sl.shipment_location_id = `job_items->exceptions->lid`.shipment_location_id LIMIT 1)"
										),
										"location_name",
									],
								],
								as: "lid",
							},
						],
					},
					{
						model: qr_code,
						as: "item_qr",
						attributes: [
							"random_number",
							"type",
							[sequelize.literal("LPAD(label_number, 8, '0')"), "label_number"],
						],
					},
					{
						model: staff,
						as: "disassembled_user",
						attributes: ["first_name", "last_name"],
					},
					{
						model: shipment_inventory_photo,
						attributes: [
							["shipment_inventory_photo_id", "photo_id"],
							"media",
							[
								sequelize.literal(
									`CASE 
									 WHEN media IS NOT NULL AND local_stage_id IS NULL 
									 THEN CONCAT('${AWS_ITEM_PATH}', media) 
									 ELSE CONCAT('${AWS_INVENTORY_PATH}', job_items.shipment_inventory_id, '/original/', media) 
									 END`
								),
								"item_photo",
							],
						],
						required: false,
						as: "item_photos",
					},
				],
			},

			{
				model: company,
				as: "job_company",
				required: false,
				attributes: [
					...COMMON_COMPANY_ATTRIBUTES,
					[
						sequelize.literal(
							`CASE 
							 WHEN job_company.photo IS NULL THEN '' 
							 ELSE CONCAT('${AWS_COMPANY_PATH}', job_company.photo) 
							 END`
						),
						"company_logo",
					],
				],
			},

			{
				model: unit_list,
				attributes: [
					"unit_id", "storage_unit_id", "shipment_job_id", "unitCode",
					"addedBy", "status", "isActive", "warehouseId", "name"
				],
				required: false,
				where: { shipment_job_id: shipmentId },
				as: "job_warehouses",
			},

			{
				model: shipment_job_document,
				as: "job_documents",
				required: false,
				attributes: [
					"shipment_job_document_id",
					"pdf",
					"shipment_job_id",
					"name",
					[
						sequelize.literal(
							`CASE 
							 WHEN job_documents.pdf IS NULL THEN '' 
							 WHEN job_documents.pdf NOT LIKE 'http%' THEN CONCAT('${AWS_DOCUMENT_PATH}', job_documents.pdf) 
							 ELSE job_documents.pdf 
							 END`
						),
						"PDF_URL",
					],
				],
			},
		],

		attributes: [
			...COMMON_JOB_ATTRIBUTES,
			"shipment_name",
			[sequelize.literal(`is_job_complete_flag = '1'`), "is_job_complete"],

			// Optimized EXISTS queries instead of COUNT > 0
			[
				sequelize.literal(
					"EXISTS(SELECT 1 FROM unit_lists ul WHERE ul.shipment_job_id = shipment_job.shipment_job_id)"
				),
				"is_shipment_assign_to_units"
			],

			[
				sequelize.literal(
					"(SELECT order_of_stages = 1 FROM shipment_type_stage_for_shipments stss WHERE stss.local_shipment_stage_id = shipment_job.local_job_status LIMIT 1)"
				),
				"is_first_stage",
			],

			// Consolidated inventory statistics
			[sequelize.literal(`(SELECT COUNT(*) FROM shipment_inventories si WHERE ${baseInventoryFilter})`), "total_items"],
			[sequelize.literal(`(SELECT COALESCE(SUM(volume), 0) FROM shipment_inventories si WHERE ${baseInventoryFilter})`), "total_volume"],
			[sequelize.literal(`(SELECT COALESCE(SUM(weight), 0) FROM shipment_inventories si WHERE ${baseInventoryFilter})`), "total_weight"],
			[sequelize.literal(`(SELECT COUNT(*) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_firearm = '1')`), "firearms_total_quantity"],
			[sequelize.literal(`(SELECT COUNT(*) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_carton = '1')`), "total_cartons"],
			[sequelize.literal(`(SELECT COUNT(*) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_carton = '1' AND packed_by_owner = '0')`), "total_cartons_cp"],
			[sequelize.literal(`(SELECT COUNT(*) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_carton = '1' AND packed_by_owner = '1')`), "total_cartons_pbo"],
			[sequelize.literal(`(SELECT COALESCE(SUM(declared_value), 0) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_high_value = '1')`), "total_high_value"],
			[sequelize.literal(`(SELECT COALESCE(SUM(pads_used), 0) FROM shipment_inventories si WHERE ${baseInventoryFilter})`), "total_pads_used"],
			[sequelize.literal(`(SELECT COALESCE(SUM(pro_gear_weight), 0) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_pro_gear = '1')`), "total_pro_gear_weight"],
			[sequelize.literal(`(SELECT COUNT(*) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_pro_gear = '1')`), "total_pro_gear_items"],
			[sequelize.literal(`(SELECT COUNT(*) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_high_value = '1')`), "total_highValue_items"],
			[sequelize.literal(`(SELECT COUNT(*) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_disassembled = '1')`), "total_disassembled_items"],
			[sequelize.literal(`(SELECT COUNT(*) FROM shipment_inventories si WHERE ${baseInventoryFilter} AND is_electronics = '1')`), "total_electronics_items"],

			// Storage and scanning flags - optimized
			[
				sequelize.literal(
					"(SELECT (scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND `shipment_job`.warehouseId IS NOT NULL) FROM shipment_type_stage_for_shipments stss WHERE stss.local_shipment_stage_id = shipment_job.local_job_status LIMIT 1)"
				),
				"scan_into_storage",
			],
			[
				sequelize.literal(
					"(SELECT (assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND `shipment_job`.warehouseId IS NOT NULL) FROM shipment_type_stage_for_shipments stss WHERE stss.local_shipment_stage_id = shipment_job.local_job_status LIMIT 1)"
				),
				"assign_storage_units_to_items",
			],
			[
				sequelize.literal(
					"(SELECT (scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND `shipment_job`.warehouseId IS NOT NULL) FROM shipment_type_stage_for_shipments stss WHERE stss.local_shipment_stage_id = shipment_job.local_job_status LIMIT 1)"
				),
				"scan_out_of_storage",
			],
			[
				sequelize.literal(
					"(SELECT (unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND `shipment_job`.warehouseId IS NOT NULL) FROM shipment_type_stage_for_shipments stss WHERE stss.local_shipment_stage_id = shipment_job.local_job_status LIMIT 1)"
				),
				"unassign_storage_units_from_items",
			],

			// Delivery date optimization
			[
				sequelize.literal(
					`CASE WHEN (
						SELECT local_shipment_stage_id 
						FROM shipment_type_stage_for_shipments stss1
						INNER JOIN shipment_type_for_shipments st ON stss1.local_shipment_type_id = st.local_shipment_type_id 
						WHERE st.local_shipment_type_id = shipment_job.local_shipment_type_id 
						ORDER BY order_of_stages DESC LIMIT 1
					) = local_job_status 
					THEN shipment_job.updated_at 
					ELSE NULL END`
				),
				"actual_delivery",
			],

			// Unit assignment checks - optimized with single queries
			[
				sequelize.literal(
					`(SELECT COUNT(*) FROM shipment_inventories si1 WHERE ${baseInventoryFilter} AND isScannedFlag = '0') = 
					 (SELECT COUNT(*) FROM shipment_inventories si2 WHERE ${baseInventoryFilter} AND storage_unit_id IS NOT NULL AND isScannedFlag = '0')`
				),
				"IsAllItemsAssignToUnits"
			],
			[
				sequelize.literal(
					`(SELECT COUNT(*) FROM shipment_inventories si1 WHERE ${baseInventoryFilter} AND isScannedFlag = '0') = 
					 (SELECT COUNT(*) FROM shipment_inventories si2 WHERE ${baseInventoryFilter} AND storage_unit_id IS NULL AND isScannedFlag = '0')`
				),
				"isAllItemsUnassignToUnits"
			],

			// Creator name - optimized
			[
				sequelize.literal(
					`CASE 
					 WHEN created_by_id IS NULL THEN NULL
					 WHEN created_by_type = 'company' THEN (SELECT company_name FROM companies c WHERE c.company_id = shipment_job.created_by_id LIMIT 1)
					 WHEN created_by_type = 'staff' THEN (SELECT first_name FROM staffs s WHERE s.staff_id = shipment_job.created_by_id LIMIT 1)
					 ELSE NULL
					 END`
				),
				'created_by_name'
			]
		],

		order: [
			["job_items", orderingMethod, orderingWay],
			["shipment_type_for_shipment", "local_shipment_stage", "order_of_stages", "ASC"],
		],
	});

	// Optimized JSON parsing - only parse if data exists
	return dbValue ? JSON.parse(JSON.stringify(dbValue, (k, v) => v === null ? "" : v)) : null;
};
exports.getShipmentDetailForCustomerModel = async (shipmentId, orderingMethod) =>
	await shipment_job.findByPk(shipmentId, {
		include: [
			{
				model: shipment_type_for_shipment,
				as: "shipment_type_for_shipment",
				attributes: [
					"local_shipment_type_id",
					[
						sequelize.literal(
							"(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
						),
						"current_job_stage",
					],
					[
						sequelize.literal(
							"(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
						),
						"total_stages",
					],
				],
				include: [
					{
						model: shipment_type_stage_for_shipment,
						as: "local_shipment_stage",
						attributes: [
							...BASIC_JOB_STAGES_ATTRIBUTES2,
							[
								sequelize.literal(
									`(select shipment_job_signatures.created_at from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
								),
								"created_at",
							],
							[
								sequelize.literal(
									`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
									}',customer_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
								),
								"customer_signature",
							],
							[
								sequelize.literal(
									`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
									}',supervisor_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id order by shipment_job_signature_id desc limit 1)`
								),
								"supervisor_signature",
							],
							[
								sequelize.literal(
									`(select customer_name from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
								),
								"customer_name",
							],
							[
								sequelize.literal(
									`(select supervisor_name from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id order by shipment_job_signature_id desc limit 1)`
								),
								"supervisor_name",
							],
						],
						order: "order_of_stages",
						where: { status: "active" },
						include: [
							{
								model: shipment_job_forced,
								as: "shipment_job_forced",
								required: false,
								attributes: COMMON_JOB_FORCED_SHIPMENT_ATTRIBUTES2,
								where: { shipment_job_id: shipmentId },
							},
						],
					},
				],
			},
			{
				model: shipment_job_assign_worker_list,
				required: false,
				attributes: [
					"staff_id",
					"role",
					[
						sequelize.literal("(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
						"first_name",
					],
					[
						sequelize.literal("(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
						"last_name",
					],
				],
				as: "assign_worker",
				include: [
					{
						model: staff,
						attributes: [],
						required: true,
						as: "assign_worker_detail",
					},
				],
			},
			{
				model: customer,
				as: "customer_job",
				attributes: COMMON_CUSTOMER_ATTRIBUTES,
			},
			{
				model: shipment_inventory,
				where: {
					shipment_inventory_id: -1
				},
				attributes: [
					...COMMON_INVENTORY_ATTRIBUTES,
					[
						sequelize.literal(
							`(IF((select shipment_job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.local_current_stage_id = shipment_job.local_job_status and shipment_inventory_job_scanned.shipment_inventory_id = job_items.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = job_items.shipment_job_id) is not null, 'yes', 'no'))`
						),
						"isScanned",
					],
					[
						sequelize.literal(
							`(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.local_current_stage_id = shipment_job.local_job_status and shipment_inventory_forced.shipment_inventory_id = job_items.shipment_inventory_id) is not null, 'yes', 'no'))`
						),
						"isOverride",
					],
					[sequelize.literal(`(IF(packed_by_owner = '1', 'OWNER', 'MOVER'))`), "packed_by"],
					[sequelize.literal(`(IF(is_disassembled = '1', 'YES', 'NO'))`), "is_disassembled"],
					[sequelize.literal(`(IF(is_carton = '1', 'YES', 'NO'))`), "is_carton"],
					[
						sequelize.literal(`(CASE WHEN is_high_value = '1' THEN 'true' ELSE 'false' END)`),
						"is_high_value",
					],
					[
						sequelize.literal(`(CASE WHEN is_pro_gear = '1' THEN 'true' ELSE 'false' END)`),
						"is_pro_gear",
					],
					[
						sequelize.literal(`(CASE WHEN is_firearm = '1' THEN 'true' ELSE 'false' END)`),
						"is_firearm",
					],
					[
						sequelize.literal(`(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`),
						"isManualLabel",
					],
					[
						sequelize.literal(`(IF(is_electronics = '1', 'YES', "NO"))`), "is_electronics"
					],
				],
				required: false,
				as: "job_items",
				include: [
					{
						model: shipment_room,
						as: "room",
						attributes: ["name"],
					},
					{
						model: shipment_inventory_comments,
						attributes: ["id",
							"shipment_inventory_id",
							"comment",],
						as: "comments",
					},
					{
						model: shipment_inventory_notes,
						attributes: ["id",
							"shipment_inventory_id",
							"note",],
						as: "inventory_notes",
					},
					{
						model: shipment_inventory_exception_note,
						attributes: [["shipment_inventory_exception_note_id", "note_id"], "notes"],
						required: false,
						as: "exceptions",
						include: [
							{
								model: shipment_inventory_exception,
								required: false,
								attributes: [
									"shipment_exception_id",
									[
										sequelize.literal(
											"(select name FROM `shipment_exceptions` where shipment_exception_id = `job_items->exceptions->eid`.`shipment_exception_id`) "
										),
										"exception_name",
									],
								],
								as: "eid",
								include: [
									{
										model: shipment_exception,
										attributes: [],
										required: true,
										as: "exception_list",
									},
								],
							},
							{
								model: shipment_inventory_location,
								required: false,
								attributes: [
									"shipment_location_id",
									[
										sequelize.literal(
											"(select name FROM `shipment_locations` where shipment_location_id = `job_items->exceptions->lid`.`shipment_location_id`) "
										),
										"location_name",
									],
								],
								as: "lid",
								include: [
									{
										model: shipment_location,
										attributes: [],
										required: true,
										as: "location_list",
									},
								],
							},
						],
					},
					{
						model: qr_code,
						as: "item_qr",
						attributes: [
							"random_number",
							"type",
							[
								sequelize.literal(

									`LPAD(label_number, 8, 0)`
								),
								"label_number",
							],
						],
						order: [["label_number", "ASC"]],
					},
					{
						model: shipment_inventory_photo,
						attributes: [
							["shipment_inventory_photo_id", "photo_id"],
							"media",
							[
								sequelize.literal(
									`(CASE WHEN media IS NOT NULL AND local_stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', job_items.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', job_items.shipment_inventory_id, '/original/', media) END)`
								),
								"item_photo",
							],
						],
						required: false,
						as: "item_photos",
					},
				],
			},
			{
				model: company,
				as: "job_company",
				attributes: [
					...COMMON_COMPANY_ATTRIBUTES,
					[
						sequelize.literal(
							`(CASE WHEN job_company.photo IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE_Company_Profile}', 'original/', job_company.photo) END)`
						),
						"company_logo",
					],
				],
			},
		],
		attributes: [...CUSTOMER_JOB_ATTRIBUTES, "shipment_name"],
		order: [
			["job_items", orderingMethod ? orderingMethod : "created_at", "ASC"],
			["shipment_type_for_shipment", "local_shipment_stage", "order_of_stages", "ASC"],
		],
	});

exports.listShipmentModel = async (company_id, fieldsAndValues) => {
	const {
		page_size = 10,
		page_no = 1,
		order_by_fields = 'created_at',
		order_sequence = 'DESC',
		search = '',
		start_date,
		end_date,
	} = fieldsAndValues;

	const dateFilters = [];
	if (start_date && end_date) {
		const start = new Date(`${start_date}T00:00:00.000Z`);
		const end = new Date(`${end_date}T23:59:59.999Z`);
		dateFilters.push({ pickup_date: { [Op.between]: [start, end] } });
	}

	const orConditions = [];
	const trimmedSearch = search.trim();

	if (trimmedSearch) {
		const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
		if (dateRegex.test(trimmedSearch)) {
			const [m, d, y] = trimmedSearch.split('/').map(Number);
			const start = new Date(Date.UTC(y, m - 1, d, 0, 0, 0));
			const end = new Date(Date.UTC(y, m - 1, d + 1, 0, 0, 0));
			orConditions.push({ pickup_date: { [Op.gte]: start, [Op.lt]: end } });
		}

		const like = (field) => ({ [field]: { [Op.like]: `%${trimmedSearch}%` } });
		const stringFields = [
			'shipment_job_id', 'job_number', 'email', 'shipment_name',
			'pickup_address', 'pickup_state', 'pickup_city', 'pickup_zipcode', 'pickup_country',
			'delivery_address', 'delivery_state', 'delivery_city', 'delivery_zipcode', 'delivery_country',
			'contact_reference', 'account_reference', 'opportunity_reference', 'wo_reference',
			'external_reference', 'external_reference_2', 'external_reference_3'
		];

		const reversedSearch = trimmedSearch.split('').reverse().join('');

		stringFields.forEach(field => {
			orConditions.push(like(field, trimmedSearch));
			orConditions.push(like(field, reversedSearch));
		});


		orConditions.push(
			where(
				fn('concat', col('`customer_job.first_name`'), ' ', col('`customer_job.last_name`')),
				{ [Op.like]: `%${trimmedSearch}%` }
			)
		);
		orConditions.push(
			where(
				fn('concat', col('`customer_job.first_name`'), ' ', col('`customer_job.last_name`')),
				{ [Op.like]: `%${reversedSearch}%` }
			)
		);
	}

	const whereClauses = [
		...(company_id ? [{ company_id }] : []),
		...dateFilters,
		...(orConditions.length ? [{ [Op.or]: orConditions }] : []),
	];

	return await shipment_job.findAndCountAll({
		limit: parseInt(page_size, 10),
		offset: (Math.max(page_no, 1) - 1) * page_size,
		where: { [Op.and]: whereClauses },
		attributes: COMMON_JOB_SHIPMENT_ATTRIBUTES,
		order: [[order_by_fields, order_sequence]],
		distinct: true,
		include: [
			{ model: tag_shipment, as: 'shipment_tag', required: false, attributes: [COMMON_JOB_TAG_ATTRIBUTES[0]], include: { model: tag, as: 'm2m_tag', attributes: COMMON_TAG_ATTRIBUTES } },
			{ model: shipment_type_for_shipment, as: 'shipment_type_for_shipment', attributes: ['local_shipment_type_id', 'name'] },
			{
				model: shipment_type_stage_for_shipment, as: 'local_shipment_job_status', attributes: [
					...BASIC_JOB_STAGES_ATTRIBUTES2,
					[
						sequelize.literal(
							`(CASE WHEN (SELECT local_altered_stage_id FROM shipment_job_forced WHERE shipment_job_id = shipment_job.shipment_job_id AND local_job_status = local_altered_stage_id ORDER BY forced_status_id DESC LIMIT 1) IS NOT NULL THEN TRUE ELSE FALSE END)`
						),
						'is_forced'
					]
				]
			},
			{
				model: customer, as: 'customer_job', required: true, attributes: [
					'customer_id', 'email',
					[sequelize.literal(`CASE WHEN last_name IS NULL THEN first_name ELSE CONCAT(first_name, ' ', last_name) END`), 'full_name'],
					'is_invited'
				]
			}
		]
	});
};

exports.getShipmentStageIdForForceStageChange = async (shipmentId) => {
	return await shipment_job.findOne({
		where: {
			shipment_job_id: shipmentId
		},
		attributes: ["shipment_job_id", "job_number", "local_job_status"]
	})
}

exports.basicShipmentModel = async () =>
	await shipment_job.findAll({ attributes: ["shipment_job_id", "job_number"] });

exports.isValidShipmentIdModel = async (shipmentId) =>
	await shipment_job.findByPk(shipmentId, {
		attributes: ["shipment_job_id", "customer_id", "email"],
	});

exports.findCustomerStatusById = async (customerId) =>
	await customer.findByPk(customerId);

exports.findShipmentCurrentStage = async (shipmentId) =>
	await shipment_job.findOne({
		where: {
			shipment_job_id: shipmentId
		},
		attributes: ["shipment_job_id", "job_number", "local_job_status"]
	})


exports.findShipmentStatusById = async (shipmentId) =>
	await shipment_job.findByPk(shipmentId, {
		attributes: [
			"shipment_job_id",
			"is_job_complete_flag",
			"email",
			[
				sequelize.literal(
					"(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
				),
				"is_first_stage",
			],],
	});

// after stage add in add items
exports.checkshipmentsValidItems = async (shipmentId, forceStageChangeId) =>
	await shipment_inventory.findAndCountAll(
		{
			logging: console.log,
			where:
			{
				shipment_job_id: shipmentId,
				inventory_stage_id: forceStageChangeId.local_job_status
			},
		}
	);

exports.basicShipmentStagesModel = async (filter) =>
	await shipment_type_stage_for_shipment.findAndCountAll({
		attributes: BASIC_JOB_STAGES_ATTRIBUTES2,
		where: {
			[Op.or]: {
				name: { [Op.like]: "%" + filter.search + "%" },
			},
		},
		order: [["name"]],
	});

const isExistsShipmentJobWorker = async (shipment_job_id, staff_worker_id, role) => {
	const isValidStaff = await shipment_job_assign_worker
		.findOne({
			where: {
				shipment_job_id: shipment_job_id,
				staff_id: staff_worker_id,
				role: role,
			},
		});
	if (isValidStaff !== null) {
		return true
	}
	else {
		return false
	}
}


exports.assignShipmentJobWorker = async (shipment_job_id, role, staff_worker_id) => {
	const isPresent = await isExistsShipmentJobWorker(shipment_job_id, staff_worker_id, role)
	let notThereFlag = false;
	if (role === "supervisor") {
		const userDetails = await isExistingShipmentJobSupervisor(shipment_job_id, role);
		if (userDetails !== null) {
			let newData = await shipment_job_assign_worker
				.destroy({
					where: {
						shipment_job_worker_id: userDetails.shipment_job_worker_id,
						shipment_job_id: userDetails.shipment_job_id,
						role: userDetails.role,
					},
				});
			return await shipment_job_assign_worker.create({
				shipment_job_id: shipment_job_id,
				staff_id: staff_worker_id,
				role: role,
			});
		} else {
			return await shipment_job_assign_worker.create({
				shipment_job_id: shipment_job_id,
				staff_id: staff_worker_id,
				role: role,
			});
		}
	} else {
		const userDetails = await isExistingShipmentJobSupervisor(shipment_job_id, role);
		if (userDetails && userDetails.role === "supervisor") {
			let newData = await shipment_job_assign_worker
				.destroy({
					where: {
						shipment_job_worker_id: userDetails.shipment_job_worker_id,
						shipment_job_id: userDetails.shipment_job_id,
						role: userDetails.role,
					},
				});
			return await shipment_job_assign_worker.create({
				shipment_job_id: shipment_job_id,
				staff_id: staff_worker_id,
				role: role,
			});

		} else if (userDetails && userDetails.role === "worker") {
			const data = await isExistingShipmentJobSupervisor(shipment_job_id, role);
			return await shipment_job_assign_worker.create({
				shipment_job_id: shipment_job_id,
				staff_id: staff_worker_id,
				role: role,
			});
		} else {
			return await shipment_job_assign_worker.create({
				shipment_job_id: shipment_job_id,
				staff_id: staff_worker_id,
				role: role,
			});
		}
	}
}

exports.unlinkShipmentJobWorker = async (shipment_job_id, staff_worker_id, role) => {
	const isPresent = await isShipmentJobWorker(shipment_job_id, staff_worker_id, role);
	if (isPresent)
		return shipment_job_assign_worker.destroy({
			where: {
				shipment_job_worker_id: staff_worker_id,
				shipment_job_id: shipment_job_id,
				role: role,
			},
		});
	else return Promise.reject("Provided details do not match with any shipment job");

}

const isShipmentJobWorker = async (shipment_job_id, staff_worker_id, role) => {
	const isStaff = shipment_job_assign_worker
		.findOne({
			where: {
				shipment_job_worker_id: staff_worker_id,
				shipment_job_id: shipment_job_id,
				role: role,
			},
		})

	if (isStaff !== null) return isStaff;
	else return false;
}

const isExistingShipmentJobSupervisor = async (shipment_job_id, role) => {
	const data = await shipment_job_assign_worker
		.findOne({
			where: {
				shipment_job_id: shipment_job_id,
				role: role,
			},
			attributes: null,
			include: {
				model: staff,
				where: { is_deleted: 0, status: "active" },
				as: "job_worker_detail",
				attributes: [[sequelize.literal('CONCAT(first_name," ",last_name)'), "full_name"]],
			},
			raw: true,
		})
	if (data !== null) {
		return data
	}
	else {
		return null
	}
}

exports.updateCustomerCountModel = async (customerId) => {
	const countData = await getShipmentDetailByCustomerId(customerId);
	return customer.update(
		{ total_shipment: countData.count },
		{
			where: {
				customer_id: customerId,
				[Op.or]: [{ is_deleted: 0 }, { is_deleted: null }],
			},
		}
	)
};

const getShipmentInitialStageModel = async (shipmentTypeId) => {
	const stageDetails = await shipment_type_for_shipment.findOne({
		attributes: ["local_shipment_type_id"],
		include: {
			model: shipment_type_stage_for_shipment,
			as: "local_shipment_stage",
			required: true,
			attributes: [BASIC_JOB_STAGES_ATTRIBUTES2[0], "local_shipment_stage_id"],
			order: [["order_of_stages", "ASC"]],
			limit: 1,
		},
		where: { local_shipment_type_id: shipmentTypeId },
	});


	if (stageDetails) {
		return stageDetails.shipment_stage.local_shipment_stage_id ? stageDetails.shipment_stage.local_shipment_stage_id : null
	}
}
const getShipmentNextStageModel = async (shipmentJobId, currentStageInfo) => {
	const shipmentStages = await shipment_type_stage_for_shipment
		.findAll({
			where: {
				shipment_job_id: shipmentJobId,
				status: "active"
			},
			attributes: [
				"name",
				"order_of_stages",
				"local_shipment_stage_id",
				"shipment_job_id"
			],
			order: [["order_of_stages", "ASC"]],
		})

	currentIndex = shipmentStages.findIndex(stage => (
		stage.order_of_stages === currentStageInfo.order_of_stages
	))


	let nextStageDetails = null;
	if (currentIndex !== -1 && currentIndex < shipmentStages.length - 1) {
		nextStageDetails = shipmentStages[currentIndex + 1].local_shipment_stage_id;
	}

	return nextStageDetails
}

exports.forceUpdateWorFlowModel = async (shipmentId, reason, altered_stage) => {
	const job_status = await shipment_job.findByPk(shipmentId, { attribute: COMMON_JOB_SHIPMENT_ATTRIBUTES[1] })
	if (parseInt(altered_stage) !== job_status.local_job_status) {
		const data = await shipment_job_forced
			.create({
				reason: reason,
				shipment_job_id: shipmentId,
				local_current_stage_id: job_status.local_job_status,
				current_stage_id: null,
				altered_stage_id: null,
				local_altered_stage_id: altered_stage,

			})
		if (data) {
			const currentStage = await shipment_type_stage_for_shipment.findOne({
				where: {
					shipment_job_id: shipmentId,
					local_shipment_stage_id: job_status.local_job_status
				},
				attributes: ["order_of_stages", "local_shipment_stage_id"]
			});

			const alterStage = await shipment_type_stage_for_shipment.findOne({
				where: {
					shipment_job_id: shipmentId,
					local_shipment_stage_id: altered_stage
				},
				attributes: ["order_of_stages", "local_shipment_stage_id"]
			});

			if (currentStage) {
				const allStages = await shipment_type_stage_for_shipment.findAll({
					where: {
						shipment_job_id: shipmentId,
						order_of_stages: {
							[Op.gte]: alterStage.order_of_stages,
							[Op.lte]: currentStage.order_of_stages
						}
					},
				});

				for (const stage of allStages) {
					if (stage.unassign_storage_units_from_items === 1) {
						await shipment_inventory.update(
							{
								is_item_scanned_remove_from_storage: 0
							},
							{
								where: {
									shipment_job_id: shipmentId
								}
							})
					}
					if (stage.remove_items_to_inventor === 1) {
						await shipment_inventory.update(
							{
								isScannedFlag: 0
							},
							{
								where: {
									shipment_job_id: shipmentId
								}
							})
					}
				}
				return shipment_job.update({ local_job_status: altered_stage }, { where: { shipment_job_id: shipmentId } })
			}
		};
	}
	else {
		return Promise.reject("Change stage cannot be same as current stage");
	}
};

exports.changeShipmentStage = async (shipmentId, stage) => {
	let currentStageInfo = await shipment_type_stage_for_shipment.findOne({
		where: { local_shipment_stage_id: stage },
		attributes: ["name", "order_of_stages", "local_shipment_stage_id"]
	})
	let nextStage = await getShipmentNextStageModel(shipmentId, currentStageInfo);
	if (nextStage) {
		return shipment_job.update({ local_job_status: nextStage }, { where: { shipment_job_id: shipmentId } });
	} else {
		const jobCompleteFlag = await homeModel.jobCompleteFlag(shipmentId);
		return Promise.resolve("No next stage available to shift");
	}
};

exports.addUpdateTagToShipmentModel = (tags, shipmentId) => {
	tag_shipment.destroy({ where: { shipment_id: shipmentId } });
	tag_shipment.bulkCreate(tags);
};

exports.checkShipmentAssignToJob = async (id) => {
	return await shipment_inventory.findAndCountAll({
		where: {
			shipment_job_id: id,
		},
	});
};

exports.getInventoryListForPDF = async (shipmentId) => {
	const data = await shipment_inventory.findAll({
		where: { shipment_job_id: shipmentId },
		attributes: [
			...COMMON_INVENTORY_ATTRIBUTES,
			[sequelize.literal(`(IF(packed_by_owner = '1', 'OWNER', 'MOVER'))`), "packed_by"],
			[
				sequelize.literal(
					`(CASE WHEN disassembled_by_owner = '1' THEN 'By Customer' ELSE 'By Company' END)`
				),
				"disassembled_by",
			],
			[sequelize.literal(`(IF(is_disassembled = '1', 'YES', 'NO'))`), "is_disassembled"],
			[sequelize.literal(`(IF(is_electronics = '1', 'YES', "NO"))`), "is_electronics"],
			[
				sequelize.literal(`(CASE WHEN is_high_value = '1' THEN 'true' ELSE 'false' END)`),
				"is_high_value",
			],
			[
				sequelize.literal(`(CASE WHEN is_pro_gear = '1' THEN 'true' ELSE 'false' END)`),
				"is_pro_gear",
			],
			[
				sequelize.literal(`(CASE WHEN is_carton = '1' THEN 'true' ELSE 'false' END)`),
				"is_carton",
			],
			[
				sequelize.literal(`(CASE WHEN is_firearm = '1' THEN 'true' ELSE 'false' END)`),
				"is_firearm",
			],
			[
				sequelize.literal(
					"(select name FROM `shipment_type_stage_for_shipments` where  local_shipment_stage_id = shipment_inventory.inventory_stage_id)"
				),
				"inventory_stage_name",
			],
			[
				sequelize.literal(`(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`),
				"isManualLabel",
			],
			[
				sequelize.literal(`(IF(is_electronics = '1', 'YES', "NO"))`), "is_electronics"
			],
		],
		required: false,
		include: [
			{
				model: unit_list,
				attributes: ["unit_id", "name", "storage_unit_id", "unitCode", "currentLocation", "unitTypeName"],
				required: false,
				as: "unit_list",
			},
			{
				model: shipment_room,
				as: "room",
				attributes: ["name"],
			},
			{
				model: shipment_inventory_exception_note,
				attributes: [["shipment_inventory_exception_note_id", "note_id"], "notes"],
				required: false,
				as: "exceptions",
				include: [
					{
						model: shipment_inventory_exception,
						required: false,
						attributes: [
							"shipment_exception_id",
							[
								sequelize.literal(
									"(select name FROM `shipment_exceptions` where shipment_exception_id = `exceptions->eid`.`shipment_exception_id`) "
								),
								"exception_name",
							],
						],
						as: "eid",
						include: [
							{
								model: shipment_exception,
								attributes: [],
								required: true,
								as: "exception_list",
							},
						],
					},
					{
						model: shipment_inventory_location,
						required: false,
						attributes: [
							"shipment_location_id",
							[
								sequelize.literal(
									"(select name FROM `shipment_locations` where shipment_location_id = `exceptions->lid`.`shipment_location_id`) "
								),
								"location_name",
							],
						],
						as: "lid",
						include: [
							{
								model: shipment_location,
								attributes: [],
								required: true,
								as: "location_list",
							},
						],
					},
				],
			},
			{
				model: qr_code,
				as: "item_qr",
				attributes: [
					"random_number",
					"type",
					[
						sequelize.literal(
							`LPAD(label_number, 8, 0)`
						),
						"label_number",
					],
					[sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`), "qr_image"],
				],
			},
			{
				model: shipment_inventory_photo,
				attributes: [
					["shipment_inventory_photo_id", "photo_id"],
					"media",
					[
						sequelize.literal(
							`(CASE WHEN media IS NOT NULL AND local_stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', shipment_inventory.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', media) END)`
						),
						"item_photo",
					],
				],
				required: false,
				as: "item_photos",
			},
			{
				model: staff,
				as: "disassembled_user",
				attributes: ["first_name", "last_name"],
			},
			{
				model: tag_item,
				as: "item_tag",
				required: false,
				attributes: [
					COMMON_ITEM_TAG_ATTRIBUTES[0],
					[sequelize.literal("`item_tag->m2m_item_tag`.tag_id"), "tag_id"],
					[sequelize.literal("`item_tag->m2m_item_tag`.name"), "name"],
					[sequelize.literal("`item_tag->m2m_item_tag`.color"), "color"],
					[sequelize.literal("`item_tag->m2m_item_tag`.company_id"), "company_id"],
				],
				include: {
					// required: false,
					model: tag,
					as: "m2m_item_tag",
					attributes: [],
				},
			},
			{
				model: shipment_inventory_comments,
				attributes: ["id", "shipment_inventory_id", "comment"],
				as: "comments",
			},
			{
				model: shipment_inventory_notes,
				attributes: ["id", "shipment_inventory_id", "note"],
				as: "inventory_notes",
			},
		],
		order: [["qr_id", "ASC"]],
	})
	return data
}

exports.getCustomerInfoforPdf = async (customerId) => {
	return customer.findOne({
		where: {
			customer_id: customerId
		}
	})
}


exports.getShipmentDetailModelForPdf = async (shipmentId) => {
	const dbValue = await shipment_job
		.findOne({
			where: {
				shipment_job_id: shipmentId,
			},
			include: [
				{
					model: shipment_type_for_shipment,
					as: "shipment_type_for_shipment",
					attributes: [
						"local_shipment_type_id",
						"name",
					],
					include: [
						{
							model: shipment_type_stage_for_shipment,
							as: "local_shipment_stage",
							attributes: [
								"name",
								"order_of_stages",
								"why_supervisor_signature_require_note",
								"why_customer_signature_require_note",
								"PDF_time_require",

								[
									sequelize.literal(
										`(select shipment_job_signatures.created_at from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
									),
									"created_at",
								],
								[
									sequelize.literal(
										`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
										}',customer_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
									),
									"customer_signature",
								],
								[
									sequelize.literal(
										`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
										}',supervisor_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id order by shipment_job_signature_id desc limit 1)`
									),
									"supervisor_signature",
								],

								[
									sequelize.literal(
										`(select customer_name from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
									),
									"customer_name",
								],
								[
									sequelize.literal(
										`(select supervisor_name from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id order by shipment_job_signature_id desc limit 1)`
									),
									"supervisor_name",
								],

								[
									sequelize.literal(
										`(CASE WHEN supervisor_signature_require_at_origin_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"supervisor_signature_require_at_origin_to_all_pages",
								],
								[
									sequelize.literal(
										`(CASE WHEN supervisor_signature_require_at_destination_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"supervisor_signature_require_at_destination_to_all_pages",
								],

								[
									sequelize.literal(
										`(CASE WHEN customer_signature_require_at_origin_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"customer_signature_require_at_origin_to_all_pages",
								],

								[
									sequelize.literal(
										`(CASE WHEN customer_signature_require_at_destination_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"customer_signature_require_at_destination_to_all_pages",
								],
							],
							order: ["order_of_stages", "ASC"],
							where: { status: "active" },

						},
					],
				},
				{
					model: shipment_job_assign_worker_list,
					required: false,
					attributes: [
						"staff_id",
						"role",
						[
							sequelize.literal("(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
							"first_name",
						],
						[
							sequelize.literal("(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
							"last_name",
						],
					],
					as: "assign_worker",
					include: [
						{
							model: staff,
							attributes: [],
							required: true,
							as: "assign_worker_detail",
						},
					],
				},
				{
					model: customer,
					as: "customer_job",
					attributes: [
						"first_name",
						"last_name",
						"email",
						"phone",
					]
				},
				{
					model: company,
					as: "job_company",
					required: false,
					attributes: [
						...COMMON_COMPANY_ATTRIBUTES,
						[
							sequelize.literal(
								`(CASE WHEN job_company.photo IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE_Company_Profile}', 'original/', job_company.photo) END)`
							),
							"company_logo",
						],
					],
				},
			],
			attributes: [
				...COMMON_JOB_ATTRIBUTES,
				"shipment_name",
				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_items",
				],

				[
					sequelize.literal(
						'(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_volume",
				],
				[
					sequelize.literal(
						'(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
					),
					"firearms_total_quantity",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
					),
					"total_cartons",
				],


				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
					),
					"total_cartons_cp",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
					),
					"total_cartons_pbo",
				],

				[
					sequelize.literal(
						'(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
					),
					"total_high_value",
				],

				[
					sequelize.literal(
						'(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_pads_used",
				],

				[
					sequelize.literal(
						'(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_disassembled = "1")'
					),
					"total_disassembled_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_electronics = "1")'
					),
					"total_electronics_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1")'
					),
					"total_highValue_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1")'
					),
					"total_is_pro_gear_items",
				],

				[
					sequelize.literal(
						"(IF((select local_shipment_stage_id from shipment_type_stage_for_shipments inner join shipment_type_for_shipments st on shipment_type_stage_for_shipments.local_shipment_type_id = st.local_shipment_type_id where st.local_shipment_type_id = shipment_job.local_shipment_type_id order by order_of_stages desc limit 1) = local_job_status,  `shipment_job`.updated_at, ''))"
					),
					"actual_delivery",
				],
			],
		});
	if (dbValue) return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)));
}


exports.getShipmentDetailModelForPicklistPdf = async (shipmentId) => {
	const dbValue = await shipment_job
		.findOne({
			where: {
				shipment_job_id: shipmentId,
			},
			include: [
				{
					model: shipment_type_for_shipment,
					as: "shipment_type_for_shipment",
					attributes: [
						"local_shipment_type_id",
						"name",
					],
					include: [
						{
							model: shipment_type_stage_for_shipment,
							as: "local_shipment_stage",
							attributes: [
								"name",
								"order_of_stages",
								"why_supervisor_signature_require_note",
								"why_customer_signature_require_note",

								[
									sequelize.literal(
										`(select shipment_job_signatures.created_at from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
									),
									"created_at",
								],
								[
									sequelize.literal(
										`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
										}',customer_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id  order by shipment_job_signature_id desc limit 1)`
									),
									"customer_signature",
								],
								[
									sequelize.literal(
										`(select CONCAT('${Const_AWS_BASE_Job_Signature + shipmentId + "/original/"
										}',supervisor_signature) from shipment_job_signatures inner join shipment_type_stage_for_shipments on local_stage = shipment_type_stage_for_shipments.local_shipment_stage_id where shipment_job_signatures.shipment_job_id = ${shipmentId} and shipment_type_stage_for_shipments.local_shipment_stage_id = \`shipment_type_for_shipment->local_shipment_stage\`.local_shipment_stage_id order by shipment_job_signature_id desc limit 1)`
									),
									"supervisor_signature",
								],

								[
									sequelize.literal(
										`(CASE WHEN supervisor_signature_require_at_origin_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"supervisor_signature_require_at_origin_to_all_pages",
								],
								[
									sequelize.literal(
										`(CASE WHEN supervisor_signature_require_at_destination_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"supervisor_signature_require_at_destination_to_all_pages",
								],

								[
									sequelize.literal(
										`(CASE WHEN customer_signature_require_at_origin_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"customer_signature_require_at_origin_to_all_pages",
								],

								[
									sequelize.literal(
										`(CASE WHEN customer_signature_require_at_destination_to_all_pages = '1' THEN 'yes' ELSE 'no' END)`
									),
									"customer_signature_require_at_destination_to_all_pages",
								],
							],
							order: "order_of_stages",
							where: { status: "active" },

						},
					],
				},
				{
					model: shipment_job_assign_worker_list,
					required: false,
					attributes: [
						"staff_id",
						"role",
						[
							sequelize.literal("(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
							"first_name",
						],
						[
							sequelize.literal("(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "),
							"last_name",
						],
					],
					as: "assign_worker",
					include: [
						{
							model: staff,
							attributes: [],
							required: true,
							as: "assign_worker_detail",
						},
					],
				},
				{
					model: customer,
					as: "customer_job",
					attributes: [
						"first_name",
						"last_name",
						"email",
						"phone",
					]
				},
				{
					model: unit_list,
					attributes:
						[
							"unit_id",
							"storage_unit_id",
							"shipment_job_id",
							"unitCode",
							"number",
							"currentLocation",
							"numericLocation",
							"addedBy",
							"unitNotes",
							"name",
							"status",
							"isActive",
							"warehouseId",
							"customerId",
							"shipmentId",
							"roomId",
							"unitTypeId",
							"unitTypeName",
						],
					required: false,
					as: "job_units",
					include: [
						{
							model: shipment_inventory,
							attributes: [
								...COMMON_INVENTORY_ATTRIBUTES,
								[sequelize.literal(`(IF(packed_by_owner = '1', 'OWNER', 'MOVER'))`), "packed_by"],
								[
									sequelize.literal(
										`(CASE WHEN disassembled_by_owner = '1' THEN 'By Customer' ELSE 'By Company' END)`
									),
									"disassembled_by",
								],
								[sequelize.literal(`(IF(is_disassembled = '1', 'YES', 'NO'))`), "is_disassembled"],
								[sequelize.literal(`(IF(is_electronics = '1', 'YES', "NO"))`), "is_electronics"],
								[
									sequelize.literal(`(CASE WHEN is_high_value = '1' THEN 'true' ELSE 'false' END)`),
									"is_high_value",
								],
								[
									sequelize.literal(`(CASE WHEN is_pro_gear = '1' THEN 'true' ELSE 'false' END)`),
									"is_pro_gear",
								],
								[
									sequelize.literal(`(CASE WHEN is_carton = '1' THEN 'true' ELSE 'false' END)`),
									"is_carton",
								],
								[
									sequelize.literal(`(CASE WHEN is_firearm = '1' THEN 'true' ELSE 'false' END)`),
									"is_firearm",
								],
								[
									sequelize.literal(
										"(select name FROM `shipment_type_stage_for_shipments` where  local_shipment_stage_id = `job_units->unit_items`.inventory_stage_id)"
									),
									"inventory_stage_name",
								],
								[
									sequelize.literal(`(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`),
									"isManualLabel",
								],
								[
									sequelize.literal(`(IF(is_electronics = '1', 'YES', "NO"))`), "is_electronics"
								],
							],
							required: false,
							as: "unit_items",
							include: [
								{
									model: unit_list,
									attributes: ["unit_id", "name", "storage_unit_id", "unitCode", "currentLocation", "unitTypeName"],
									required: false,
									as: "unit_list",
								},
								{
									model: shipment_room,
									as: "room",
									attributes: ["name"],
								},
								{
									model: shipment_inventory_exception_note,
									attributes: [["shipment_inventory_exception_note_id", "note_id"], "notes"],
									required: false,
									as: "exceptions",
									include: [
										{
											model: shipment_inventory_exception,
											required: false,
											attributes: [
												"shipment_exception_id",
												[
													sequelize.literal(
														"(select name FROM `shipment_exceptions` where shipment_exception_id = `job_units->unit_items->exceptions->eid`.`shipment_exception_id`) "
													),
													"exception_name",
												],
											],
											as: "eid",
											include: [
												{
													model: shipment_exception,
													attributes: [],
													required: true,
													as: "exception_list",
												},
											],
										},
										{
											model: shipment_inventory_location,
											required: false,
											attributes: [
												"shipment_location_id",
												[
													sequelize.literal(
														"(select name FROM `shipment_locations` where shipment_location_id = `job_units->unit_items->exceptions->lid`.`shipment_location_id`) "
													),
													"location_name",
												],
											],
											as: "lid",
											include: [
												{
													model: shipment_location,
													attributes: [],
													required: true,
													as: "location_list",
												},
											],
										},
									],
								},
								{
									model: qr_code,
									as: "item_qr",
									attributes: [
										"random_number",
										"type",
										[
											sequelize.literal(
												`LPAD(label_number, 8, 0)`
											),
											"label_number",
										],
									],
								},
								{
									model: staff,
									as: "disassembled_user",
									attributes: ["first_name", "last_name"],
								},
								{
									model: shipment_inventory_comments,
									attributes: ["id", "shipment_inventory_id", "comment"],
									as: "comments",
								},
								{
									model: shipment_inventory_notes,
									attributes: ["id", "shipment_inventory_id", "note"],
									as: "inventory_notes",
								},
							],
						},
					]
				},
				{
					model: company,
					as: "job_company",
					required: false,
					attributes: [
						...COMMON_COMPANY_ATTRIBUTES,
						[
							sequelize.literal(
								`(CASE WHEN job_company.photo IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE_Company_Profile}', 'original/', job_company.photo) END)`
							),
							"company_logo",
						],
					],
				},
			],
			attributes: [
				...COMMON_JOB_ATTRIBUTES,
				"shipment_name",
				[
					sequelize.literal(
						'(select count(storage_unit_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL )'
					),
					"total_items",
				],

				[
					sequelize.literal(
						'(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_volume",
				],
				[
					sequelize.literal(
						'(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
					),
					"firearms_total_quantity",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
					),
					"total_cartons",
				],


				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
					),
					"total_cartons_cp",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
					),
					"total_cartons_pbo",
				],

				[
					sequelize.literal(
						'(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
					),
					"total_high_value",
				],

				[
					sequelize.literal(
						'(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_pads_used",
				],

				[
					sequelize.literal(
						'(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_disassembled = "1")'
					),
					"total_disassembled_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_electronics = "1")'
					),
					"total_electronics_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1")'
					),
					"total_highValue_items",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1")'
					),
					"total_is_pro_gear_items",
				],

				[
					sequelize.literal(
						"(IF((select local_shipment_stage_id from shipment_type_stage_for_shipments inner join shipment_type_for_shipments st on shipment_type_stage_for_shipments.local_shipment_type_id = st.local_shipment_type_id where st.local_shipment_type_id = shipment_job.local_shipment_type_id order by order_of_stages desc limit 1) = local_job_status,  `shipment_job`.updated_at, ''))"
					),
					"actual_delivery",
				],
			],
		});
	if (dbValue) return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)));
}
